<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\CropBuyersModel;
use App\Models\CropsFarmMarketingDataModel;
use App\Models\CropsModel;
use App\Models\AdxProvinceModel;

class CropBuyersReportController extends BaseController
{
    protected $cropBuyersModel;
    protected $farmMarketingDataModel;
    protected $cropsModel;
    protected $provinceModel;

    public function __construct()
    {
        helper(['form', 'url']);
        $this->cropBuyersModel = new CropBuyersModel();
        $this->farmMarketingDataModel = new CropsFarmMarketingDataModel();
        $this->cropsModel = new CropsModel();
        $this->provinceModel = new AdxProvinceModel();
    }

    /**
     * Display comprehensive crop buyers report
     * GET /staff/reports/crop-buyers
     */
    public function index()
    {
        try {
            // Get buyer summary data with transaction information
            $buyer_summary = $this->farmMarketingDataModel->getMarketSummaryByBuyer();
            
            // Get basic buyer information with related data
            $all_buyers = $this->cropBuyersModel
                ->select('crop_buyers.*, adx_crops.crop_name, adx_province.name as province_name')
                ->join('adx_crops', 'adx_crops.id = crop_buyers.crop_id', 'left')
                ->join('adx_province', 'adx_province.id = crop_buyers.location_id AND crop_buyers.operation_span = "local"', 'left')
                ->where('crop_buyers.status', 'active')
                ->orderBy('crop_buyers.name', 'ASC')
                ->findAll();

            // Merge buyer data with transaction summary
            $buyers_data = [];
            foreach ($all_buyers as $buyer) {
                $buyer_stats = null;
                foreach ($buyer_summary as $summary) {
                    if ($summary['buyer_id'] == $buyer['id']) {
                        $buyer_stats = $summary;
                        break;
                    }
                }
                
                // Get most recent transaction date
                $last_transaction = $this->farmMarketingDataModel
                    ->select('market_date')
                    ->where('buyer_id', $buyer['id'])
                    ->where('status', 'active')
                    ->orderBy('market_date', 'DESC')
                    ->first();
                
                $buyers_data[] = [
                    'id' => $buyer['id'],
                    'buyer_code' => $buyer['buyer_code'],
                    'name' => $buyer['name'],
                    'crop_name' => $buyer['crop_name'] ?? 'Not Specified',
                    'contact_number' => $buyer['contact_number'],
                    'email' => $buyer['email'],
                    'operation_span' => ucfirst($buyer['operation_span']),
                    'location_name' => $buyer['operation_span'] === 'local' ? 
                        ($buyer['province_name'] ?? 'Unknown Province') : 'Papua New Guinea',
                    'address' => $buyer['address'],
                    'status' => $buyer['status'],
                    'created_at' => $buyer['created_at'],
                    // Transaction data
                    'transaction_count' => $buyer_stats['transaction_count'] ?? 0,
                    'total_quantity' => $buyer_stats['total_quantity'] ?? 0,
                    'total_value' => $buyer_stats['total_value'] ?? 0,
                    'avg_price' => $buyer_stats['avg_price'] ?? 0,
                    'total_freight_cost' => $buyer_stats['total_freight_cost'] ?? 0,
                    'selling_locations' => $buyer_stats['selling_locations'] ?? '',
                    'unit_of_measure' => $buyer_stats['unit_of_measure'] ?? '',
                    'last_transaction_date' => $last_transaction['market_date'] ?? null
                ];
            }

            // Calculate summary statistics
            $stats = [
                'total_buyers' => count($all_buyers),
                'active_buyers' => count(array_filter($buyers_data, fn($b) => $b['transaction_count'] > 0)),
                'inactive_buyers' => count(array_filter($buyers_data, fn($b) => $b['transaction_count'] == 0)),
                'total_transactions' => array_sum(array_column($buyer_summary, 'transaction_count')),
                'total_value' => array_sum(array_column($buyer_summary, 'total_value')),
                'avg_transaction_value' => 0,
                'top_buyer' => null
            ];

            if ($stats['total_transactions'] > 0) {
                $stats['avg_transaction_value'] = $stats['total_value'] / $stats['total_transactions'];
            }

            // Find top buyer by transaction value
            if (!empty($buyer_summary)) {
                $top_buyer_data = array_reduce($buyer_summary, function($carry, $item) {
                    return ($carry === null || $item['total_value'] > $carry['total_value']) ? $item : $carry;
                });
                $stats['top_buyer'] = $top_buyer_data['buyer_name'] ?? null;
            }

            $data = [
                'title' => 'Crop Buyers Report',
                'page_header' => 'Crop Buyers Report',
                'buyers_data' => $buyers_data,
                'stats' => $stats
            ];

            return view('staff_reports/staff_reports_crop_buyers_report', $data);

        } catch (\Exception $e) {
            log_message('error', '[Crop Buyers Report] ' . $e->getMessage());
            return redirect()->to('staff')->with('error', 'Error loading crop buyers report.');
        }
    }
}
