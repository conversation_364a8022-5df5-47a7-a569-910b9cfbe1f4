<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0">Edit Livestock Farm Block</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>" class="text-primary">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/livestock-farm-blocks') ?>" class="text-primary">Livestock Farm Blocks</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Edit</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff/livestock-farm-blocks') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                    <a href="<?= base_url('staff/livestock-farm-blocks/' . $farm_block['id']) ?>" class="btn btn-info me-2">
                        <i class="fas fa-eye me-2"></i>View Details
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>Edit Livestock Farm Block
                    </h5>
                    <p class="card-text mb-0 text-muted">Block Code: <strong><?= esc($farm_block['block_code']) ?></strong></p>
                </div>
                <div class="card-body">
                    <!-- Alert Messages -->
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i><?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>Please fix the following errors:</strong>
                            <ul class="mb-0 mt-2">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Form -->
                    <form method="post" action="<?= base_url('staff/livestock-farm-blocks/' . $farm_block['id']) ?>">
                        <?= csrf_field() ?>
                        <input type="hidden" name="_method" value="PUT">
                        
                        <div class="row">
                            <!-- Farmer Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="farmer_id" class="form-label">Farmer <span class="text-danger">*</span></label>
                                <select class="form-select" id="farmer_id" name="farmer_id" required>
                                    <option value="">Select Farmer</option>
                                    <?php foreach ($farmers as $farmer): ?>
                                        <option value="<?= $farmer['id'] ?>" 
                                                <?= (old('farmer_id', $farm_block['farmer_id']) == $farmer['id']) ? 'selected' : '' ?>>
                                            <?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?> (<?= esc($farmer['farmer_code']) ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- LLG Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="llg_id" class="form-label">LLG <span class="text-danger">*</span></label>
                                <select class="form-select" id="llg_id" name="llg_id" required onchange="filterWards()">
                                    <option value="">Select LLG</option>
                                    <?php foreach ($llgs as $llg): ?>
                                        <option value="<?= $llg['id'] ?>"
                                                <?= (old('llg_id', $farm_block['llg_id']) == $llg['id']) ? 'selected' : '' ?>>
                                            <?= esc($llg['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Ward Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="ward_id" class="form-label">Ward <span class="text-danger">*</span></label>
                                <select class="form-select" id="ward_id" name="ward_id" required>
                                    <option value="">Select Ward</option>
                                    <?php foreach ($wards as $ward): ?>
                                        <option value="<?= $ward['id'] ?>" data-llg="<?= $ward['llg_id'] ?>"
                                                <?= (old('ward_id', $farm_block['ward_id']) == $ward['id']) ? 'selected' : '' ?>>
                                            <?= esc($ward['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Village -->
                            <div class="col-md-6 mb-3">
                                <label for="village" class="form-label">Village <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="village" name="village" 
                                       value="<?= old('village', $farm_block['village']) ?>" required maxlength="100">
                            </div>
                        </div>

                        <div class="row">
                            <!-- Block Site -->
                            <div class="col-md-12 mb-3">
                                <label for="block_site" class="form-label">Block Site <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="block_site" name="block_site" 
                                       value="<?= old('block_site', $farm_block['block_site']) ?>" required maxlength="200"
                                       placeholder="Describe the specific location of the farm block">
                            </div>
                        </div>

                        <div class="row">
                            <!-- Longitude -->
                            <div class="col-md-6 mb-3">
                                <label for="lon" class="form-label">Longitude</label>
                                <input type="text" class="form-control" id="lon" name="lon" 
                                       value="<?= old('lon', $farm_block['lon']) ?>" maxlength="50"
                                       placeholder="e.g., 143.1234">
                            </div>

                            <!-- Latitude -->
                            <div class="col-md-6 mb-3">
                                <label for="lat" class="form-label">Latitude</label>
                                <input type="text" class="form-control" id="lat" name="lat" 
                                       value="<?= old('lat', $farm_block['lat']) ?>" maxlength="50"
                                       placeholder="e.g., -6.1234">
                            </div>
                        </div>

                        <div class="row">
                            <!-- Remarks -->
                            <div class="col-md-12 mb-3">
                                <label for="remarks" class="form-label">Remarks</label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3"
                                          placeholder="Additional notes or comments about this farm block"><?= old('remarks', $farm_block['remarks']) ?></textarea>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="<?= base_url('staff/livestock-farm-blocks') ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Livestock Farm Block
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize Select2 for farmer dropdown
    $('#farmer_id').select2({
        theme: 'bootstrap-5',
        width: '100%',
        placeholder: 'Search and select a farmer...',
        allowClear: true,
        minimumResultsForSearch: 0
    });

    // Trigger ward filtering on page load if LLG is already selected
    const initialLlgId = document.getElementById('llg_id').value;
    if (initialLlgId) {
        filterWards();
    }
});

// Filter wards based on selected LLG
function filterWards() {
    const llgSelect = document.getElementById('llg_id');
    const wardSelect = document.getElementById('ward_id');
    const selectedLlgId = llgSelect.value;
    const currentWardId = '<?= old('ward_id', $farm_block['ward_id']) ?>';

    if (selectedLlgId) {
        // Enable ward dropdown
        wardSelect.disabled = false;

        // Show only wards for selected LLG
        wardSelect.innerHTML = '<option value="">Select Ward</option>';

        // Get all ward options and show only matching ones
        <?php foreach ($wards as $ward): ?>
        if ('<?= $ward['llg_id'] ?>' === selectedLlgId) {
            const selected = ('<?= $ward['id'] ?>' === currentWardId) ? 'selected' : '';
            wardSelect.innerHTML += '<option value="<?= $ward['id'] ?>" ' + selected + '><?= esc($ward['name']) ?></option>';
        }
        <?php endforeach; ?>
    } else {
        // Disable ward dropdown if no LLG selected
        wardSelect.disabled = true;
        wardSelect.innerHTML = '<option value="">Select LLG first</option>';
    }
}
</script>

<?= $this->endSection() ?>
