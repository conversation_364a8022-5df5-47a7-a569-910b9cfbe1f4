<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0">Livestock Farm Blocks</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>" class="text-primary">Dashboard</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Livestock Farm Blocks</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                    <a href="<?= base_url('staff/livestock-farm-blocks/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Livestock Farm Block
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cow me-2"></i>Livestock Farm Blocks Management
                    </h5>
                    <p class="card-text mb-0 text-muted">Manage livestock farm blocks and their locations</p>
                </div>
                <div class="card-body">
                    <!-- Alert Messages -->
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i><?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i><?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($farm_blocks)): ?>
                        <!-- Table Controls -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <label for="entriesSelect" class="form-label me-2 mb-0">Show</label>
                                <select class="form-select form-select-sm me-2" id="entriesSelect" style="width: auto;">
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                                <span class="text-muted">livestock farm blocks per page</span>
                            </div>
                            <div>
                                <input type="text" class="form-control form-control-sm" id="searchInput" placeholder="Search livestock farm blocks..." style="width: 250px;">
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="livestockFarmBlocksTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Block Code</th>
                                        <th>Farmer Name</th>
                                        <th>Location</th>
                                        <th>Village</th>
                                        <th>Block Site</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($farm_blocks as $block): ?>
                                    <tr>
                                        <td><strong><?= esc($block['block_code'] ?? '') ?></strong></td>
                                        <td>
                                            <strong><?= esc(($block['given_name'] ?? '') . ' ' . ($block['surname'] ?? '')) ?></strong>
                                            <br><small class="text-muted">ID: <?= esc($block['farmer_id'] ?? '') ?></small>
                                        </td>
                                        <td><?= esc(($block['llg_name'] ?? 'N/A') . ' - ' . ($block['ward_name'] ?? 'N/A')) ?></td>
                                        <td><?= esc($block['village'] ?? '') ?></td>
                                        <td><?= esc($block['block_site'] ?? '') ?></td>
                                        <td>
                                            <?php if (($block['status'] ?? '') == 'active'): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php elseif (($block['status'] ?? '') == 'inactive'): ?>
                                                <span class="badge bg-secondary">Inactive</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary"><?= ucfirst(esc($block['status'] ?? 'unknown')) ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?= base_url('staff/livestock-farm-blocks/' . $block['id']) ?>"
                                                   class="btn btn-sm btn-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?= base_url('staff/livestock-farm-blocks/' . $block['id'] . '/edit') ?>"
                                                   class="btn btn-sm btn-warning" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form method="post" action="<?= base_url('staff/livestock-farm-blocks/' . $block['id']) ?>" 
                                                      style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this livestock farm block?')">
                                                    <input type="hidden" name="_method" value="DELETE">
                                                    <?= csrf_field() ?>
                                                    <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <!-- Empty State -->
                        <div class="text-center py-5">
                            <div class="mb-4">
                                <i class="fas fa-cow fa-4x text-muted mb-3"></i>
                                <h5 class="text-muted">No Livestock Farm Blocks Found</h5>
                                <p class="text-muted">You haven't created any livestock farm blocks yet.</p>
                            </div>
                            <a href="<?= base_url('staff/livestock-farm-blocks/create') ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Create Your First Livestock Farm Block
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize DataTable only if table exists and has data
$(document).ready(function() {
    const table = $('#livestockFarmBlocksTable');
    const entriesSelect = $('#entriesSelect');
    const searchInput = $('#searchInput');

    // Only initialize DataTable if the table exists and has data rows
    if (table.length && table.find('tbody tr').length > 0) {
        try {
            const dataTable = table.DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'asc']],
                columnDefs: [
                    { orderable: false, targets: [6] } // Actions column
                ],
                language: {
                    search: "Search livestock farm blocks:",
                    lengthMenu: "Show _MENU_ livestock farm blocks per page",
                    info: "Showing _START_ to _END_ of _TOTAL_ livestock farm blocks",
                    infoEmpty: "No livestock farm blocks found",
                    infoFiltered: "(filtered from _MAX_ total livestock farm blocks)",
                    emptyTable: "No livestock farm blocks found",
                    zeroRecords: "No matching livestock farm blocks found"
                },
                dom: 'rt<"d-flex justify-content-between align-items-center mt-3"<"text-muted"i><"pagination-wrapper"p>>',
                drawCallback: function(settings) {
                    const info = this.api().page.info();
                    const infoText = `Showing ${info.start + 1} to ${info.end} of ${info.recordsTotal} livestock farm blocks`;
                    $('.dataTables_info').text(infoText);
                }
            });

            // Handle entries per page change
            entriesSelect.on('change', function() {
                dataTable.page.len(parseInt(this.value)).draw();
            });

            // Handle search
            searchInput.on('keyup', function() {
                dataTable.search(this.value).draw();
            });

        } catch (error) {
            console.error('DataTable initialization error:', error);
            table.after('<div class="alert alert-warning mt-3"><i class="fas fa-exclamation-triangle me-2"></i>Table features are temporarily unavailable. Data is still visible above.</div>');
        }
    } else {
        // Hide controls if no data
        entriesSelect.closest('.d-flex').hide();
    }
});
</script>

<?= $this->endSection() ?>
