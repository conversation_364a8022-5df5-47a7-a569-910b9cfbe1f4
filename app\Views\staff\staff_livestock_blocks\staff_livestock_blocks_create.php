<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0">Create Livestock Farm Block</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>" class="text-primary">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/livestock-farm-blocks') ?>" class="text-primary">Livestock Farm Blocks</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Create</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff/livestock-farm-blocks') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus me-2"></i>Create New Livestock Farm Block
                    </h5>
                    <p class="card-text mb-0 text-muted">Add a new livestock farm block to the system</p>
                </div>
                <div class="card-body">
                    <!-- Alert Messages -->
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i><?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>Please fix the following errors:</strong>
                            <ul class="mb-0 mt-2">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Form -->
                    <form method="post" action="<?= base_url('staff/livestock-farm-blocks') ?>">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <!-- Farmer Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="farmer_id" class="form-label">Farmer <span class="text-danger">*</span></label>
                                <select class="form-select" id="farmer_id" name="farmer_id" required>
                                    <option value="">Select Farmer</option>
                                    <?php foreach ($farmers as $farmer): ?>
                                        <option value="<?= $farmer['id'] ?>" <?= old('farmer_id') == $farmer['id'] ? 'selected' : '' ?>>
                                            <?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?> (<?= esc($farmer['farmer_code']) ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- LLG Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="llg_id" class="form-label">LLG <span class="text-danger">*</span></label>
                                <select class="form-select" id="llg_id" name="llg_id" required>
                                    <option value="">Select LLG</option>
                                    <?php foreach ($llgs as $llg): ?>
                                        <option value="<?= $llg['id'] ?>" <?= old('llg_id') == $llg['id'] ? 'selected' : '' ?>>
                                            <?= esc($llg['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Ward Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="ward_id" class="form-label">Ward <span class="text-danger">*</span></label>
                                <select class="form-select" id="ward_id" name="ward_id" required>
                                    <option value="">Select Ward</option>
                                    <?php foreach ($wards as $ward): ?>
                                        <option value="<?= $ward['id'] ?>" <?= old('ward_id') == $ward['id'] ? 'selected' : '' ?>>
                                            <?= esc($ward['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Village -->
                            <div class="col-md-6 mb-3">
                                <label for="village" class="form-label">Village <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="village" name="village" 
                                       value="<?= old('village') ?>" required maxlength="100">
                            </div>
                        </div>

                        <div class="row">
                            <!-- Block Site -->
                            <div class="col-md-12 mb-3">
                                <label for="block_site" class="form-label">Block Site <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="block_site" name="block_site" 
                                       value="<?= old('block_site') ?>" required maxlength="200"
                                       placeholder="Describe the specific location of the farm block">
                            </div>
                        </div>

                        <div class="row">
                            <!-- Longitude -->
                            <div class="col-md-6 mb-3">
                                <label for="lon" class="form-label">Longitude</label>
                                <input type="text" class="form-control" id="lon" name="lon" 
                                       value="<?= old('lon') ?>" maxlength="50"
                                       placeholder="e.g., 143.1234">
                            </div>

                            <!-- Latitude -->
                            <div class="col-md-6 mb-3">
                                <label for="lat" class="form-label">Latitude</label>
                                <input type="text" class="form-control" id="lat" name="lat" 
                                       value="<?= old('lat') ?>" maxlength="50"
                                       placeholder="e.g., -6.1234">
                            </div>
                        </div>

                        <div class="row">
                            <!-- Remarks -->
                            <div class="col-md-12 mb-3">
                                <label for="remarks" class="form-label">Remarks</label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3"
                                          placeholder="Additional notes or comments about this farm block"><?= old('remarks') ?></textarea>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="<?= base_url('staff/livestock-farm-blocks') ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create Livestock Farm Block
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize Select2 for better dropdowns
    $('#farmer_id, #llg_id, #ward_id').select2({
        theme: 'bootstrap-5',
        width: '100%'
    });

    // Filter wards based on selected LLG (if needed)
    $('#llg_id').on('change', function() {
        const llgId = $(this).val();
        const wardSelect = $('#ward_id');
        
        if (llgId) {
            // You can implement AJAX filtering here if needed
            // For now, all wards are shown
        } else {
            wardSelect.val('').trigger('change');
        }
    });
});
</script>

<?= $this->endSection() ?>
