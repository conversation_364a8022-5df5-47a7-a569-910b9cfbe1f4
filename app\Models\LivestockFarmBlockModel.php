<?php
namespace App\Models;

use CodeIgniter\Model;

class LivestockFarmBlockModel extends Model
{
    protected $table = 'livestock_farm_blocks';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;

    protected $allowedFields = [
        'exercise_id',
        'farmer_id',
        'block_code',
        'org_id',
        'country_id',
        'province_id',
        'district_id',
        'llg_id',
        'ward_id',
        'village',
        'block_site',
        'lon',
        'lat',
        'remarks',
        'created_by',
        'updated_by',
        'deleted_by',
        'status'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    protected $dateFormat = 'datetime';

    protected $validationRules = [
        'farmer_id' => 'required|numeric',
        'block_code' => 'required|max_length[50]',
        'org_id' => 'required|numeric',
        'country_id' => 'required|numeric',
        'province_id' => 'required|numeric',
        'district_id' => 'required|numeric',
        'llg_id' => 'required|numeric',
        'ward_id' => 'required|numeric',
        'village' => 'required|max_length[100]',
        'block_site' => 'required|max_length[200]',
        'lon' => 'permit_empty|max_length[50]',
        'lat' => 'permit_empty|max_length[50]',
        'remarks' => 'permit_empty',
        'status' => 'required|max_length[50]',
        'created_by' => 'required|numeric'
    ];

    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    public function getFarmBlocksWithDetails()
    {
        return $this->select('livestock_farm_blocks.*,
                            farmer_information.given_name,
                            farmer_information.surname,
                            farmer_information.farmer_code,
                            adx_ward.name as ward_name,
                            adx_llg.name as llg_name,
                            adx_district.name as district_name,
                            adx_province.name as province_name')
            ->join('farmer_information', 'farmer_information.id = livestock_farm_blocks.farmer_id')
            ->join('adx_ward', 'adx_ward.id = livestock_farm_blocks.ward_id')
            ->join('adx_llg', 'adx_llg.id = livestock_farm_blocks.llg_id')
            ->join('adx_district', 'adx_district.id = livestock_farm_blocks.district_id')
            ->join('adx_province', 'adx_province.id = livestock_farm_blocks.province_id')
            ->where('livestock_farm_blocks.district_id', session()->get('district_id'))
            ->where('livestock_farm_blocks.status !=', 'deleted')
            ->findAll();
    }
}