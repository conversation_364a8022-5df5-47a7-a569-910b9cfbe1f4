<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0">Crop Buyers Report</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Crop Buyers Report</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                    <a href="<?= base_url('staff/crop-buyers') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Manage Buyers
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Information -->
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= number_format($stats['total_buyers']) ?></h4>
                            <p class="mb-0">Total Buyers</p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-handshake"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= number_format($stats['active_buyers']) ?></h4>
                            <p class="mb-0">Active Buyers</p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= number_format($stats['total_transactions']) ?></h4>
                            <p class="mb-0">Total Transactions</p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">K<?= number_format($stats['total_value'], 2) ?></h4>
                            <p class="mb-0">Total Value</p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Stats Row -->
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-6">
            <div class="card border-left-secondary h-100">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">Average Transaction Value</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800">K<?= number_format($stats['avg_transaction_value'], 2) ?></div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6">
            <div class="card border-left-success h-100">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Top Buyer</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['top_buyer'] ?? 'No transactions yet' ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-handshake me-2"></i>Crop Buyers Report
                    </h5>
                    <p class="card-text mb-0 text-muted">Comprehensive overview of all crop buyers and their transaction history</p>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="cropBuyersTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Buyer Code</th>
                                    <th>Buyer Name</th>
                                    <th>Primary Crop</th>
                                    <th>Operation Span</th>
                                    <th>Location</th>
                                    <th>Contact</th>
                                    <th>Transactions</th>
                                    <th>Total Value</th>
                                    <th>Last Transaction</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($buyers_data as $buyer): ?>
                                <tr>
                                    <td><strong><?= esc($buyer['buyer_code']) ?></strong></td>
                                    <td>
                                        <strong><?= esc($buyer['name']) ?></strong>
                                        <?php if (!empty($buyer['email'])): ?>
                                            <br><small class="text-muted"><?= esc($buyer['email']) ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-success"><?= esc($buyer['crop_name']) ?></span>
                                    </td>
                                    <td>
                                        <span class="badge <?= $buyer['operation_span'] === 'National' ? 'bg-info' : 'bg-secondary' ?>">
                                            <?= esc($buyer['operation_span']) ?>
                                        </span>
                                    </td>
                                    <td><?= esc($buyer['location_name']) ?></td>
                                    <td>
                                        <?php if (!empty($buyer['contact_number'])): ?>
                                            <i class="fas fa-phone me-1"></i><?= esc($buyer['contact_number']) ?>
                                        <?php else: ?>
                                            <span class="text-muted">Not provided</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?= number_format($buyer['transaction_count']) ?></strong>
                                        <?php if ($buyer['total_quantity'] > 0): ?>
                                            <br><small class="text-muted"><?= number_format($buyer['total_quantity'], 2) ?> <?= esc($buyer['unit_of_measure']) ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong>K<?= number_format($buyer['total_value'], 2) ?></strong>
                                        <?php if ($buyer['avg_price'] > 0): ?>
                                            <br><small class="text-muted">Avg: K<?= number_format($buyer['avg_price'], 2) ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($buyer['last_transaction_date']): ?>
                                            <?= date('M d, Y', strtotime($buyer['last_transaction_date'])) ?>
                                        <?php else: ?>
                                            <span class="text-muted">No transactions</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge <?= $buyer['status'] === 'active' ? 'bg-success' : 'bg-secondary' ?>">
                                            <?= ucfirst($buyer['status']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= base_url('staff/crop-buyers/' . $buyer['id']) ?>" 
                                               class="btn btn-sm btn-info" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= base_url('staff/crop-buyers/' . $buyer['id'] . '/edit') ?>" 
                                               class="btn btn-sm btn-warning" title="Edit Buyer">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#cropBuyersTable').DataTable({
        responsive: true,
        order: [[0, "desc"]],
        pageLength: 25,
        columnDefs: [
            { orderable: false, targets: -1 } // Disable sorting on Actions column
        ],
        language: {
            search: "Search Crop Buyers:",
            lengthMenu: "Show _MENU_ buyers per page",
            info: "Showing _START_ to _END_ of _TOTAL_ buyers",
            infoEmpty: "No buyers found",
            infoFiltered: "(filtered from _MAX_ total buyers)"
        }
    });
});
</script>
<?= $this->endSection() ?>
