<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\{
    LivestockFarmBlockModel,
    FarmerInformationModel,
    AdxDistrictModel,
    AdxLlgModel,
    AdxWardModel
};

class LivestockFarmBlocksController extends BaseController
{
    protected $farmBlockModel;
    protected $farmerModel;
    protected $districtModel;
    protected $llgModel;
    protected $wardModel;

    public function __construct()
    {
        $this->farmBlockModel = new LivestockFarmBlockModel();
        $this->farmerModel = new FarmerInformationModel();
        $this->districtModel = new AdxDistrictModel();
        $this->llgModel = new AdxLlgModel();
        $this->wardModel = new AdxWardModel();
        
        // Initialize helpers
        helper(['form', 'url', 'info']);
    }

    /**
     * Display a listing of livestock farm blocks
     */
    public function index()
    {
        $district_id = session()->get('district_id');
        $farm_blocks = $this->farmBlockModel->getFarmBlocksWithDetails();

        $data = [
            'title' => 'Livestock Farm Blocks',
            'page_header' => 'Livestock Farm Blocks Management',
            'farm_blocks' => $farm_blocks
        ];

        return view('staff/staff_livestock_blocks/staff_livestock_blocks_index', $data);
    }

    /**
     * Show the form for creating a new livestock farm block
     */
    public function create()
    {
        $district_id = session()->get('district_id');

        $data = [
            'title' => 'Create Livestock Farm Block',
            'page_header' => 'Create New Livestock Farm Block',
            'farmers' => $this->farmerModel->where('district_id', $district_id)
                                          ->where('status', 'active')
                                          ->findAll(),
            'llgs' => $this->llgModel->where('district_id', $district_id)->findAll(),
            'wards' => $this->wardModel->where('district_id', $district_id)->findAll()
        ];

        return view('staff/staff_livestock_blocks/staff_livestock_blocks_create', $data);
    }

    /**
     * Store a newly created livestock farm block
     */
    public function store()
    {
        $validation = \Config\Services::validation();
        
        $rules = [
            'farmer_id' => 'required|numeric',
            'llg_id' => 'required|numeric',
            'ward_id' => 'required|numeric',
            'village' => 'required|max_length[100]',
            'block_site' => 'required|max_length[200]',
            'lon' => 'permit_empty|max_length[50]',
            'lat' => 'permit_empty|max_length[50]',
            'remarks' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        // Get location data from selected LLG
        $llgId = $this->request->getPost('llg_id');
        $llg = $this->llgModel->select('country_id, province_id, district_id')->find($llgId);

        if (!$llg) {
            return redirect()->back()->withInput()->with('error', 'Invalid LLG selected.');
        }

        // Generate block code for livestock (LS prefix + farmer code + increment)
        $farmer_id = $this->request->getPost('farmer_id');
        $farmer = $this->farmerModel->find($farmer_id);
        
        if (!$farmer) {
            return redirect()->back()->withInput()->with('error', 'Invalid farmer selected.');
        }

        // Get the latest block code for this farmer
        $latest_block = $this->farmBlockModel->where('farmer_id', $farmer_id)
                                            ->orderBy('id', 'DESC')
                                            ->first();

        if ($latest_block) {
            // Extract number after the dash and increment
            $current_number = (int)substr($latest_block['block_code'], strpos($latest_block['block_code'], '-') + 1);
            $block_code = 'LS' . $farmer['farmer_code'] . '-' . sprintf('%03d', $current_number + 1);
        } else {
            // First block starts with 001
            $block_code = 'LS' . $farmer['farmer_code'] . '-001';
        }

        $data = [
            'exercise_id' => session()->get('exercise_id'), // nullable
            'farmer_id' => $farmer_id,
            'block_code' => $block_code,
            'org_id' => session()->get('org_id'),
            'country_id' => $llg['country_id'],
            'province_id' => $llg['province_id'],
            'district_id' => $llg['district_id'],
            'llg_id' => $this->request->getPost('llg_id'),
            'ward_id' => $this->request->getPost('ward_id'),
            'village' => $this->request->getPost('village'),
            'block_site' => $this->request->getPost('block_site'),
            'lon' => $this->request->getPost('lon'),
            'lat' => $this->request->getPost('lat'),
            'remarks' => $this->request->getPost('remarks'),
            'status' => 'active',
            'created_by' => session()->get('emp_id')
        ];

        try {
            $result = $this->farmBlockModel->save($data);

            if ($result === false) {
                $errors = $this->farmBlockModel->errors();
                log_message('error', '[Create Livestock Farm Block] Validation failed: ' . json_encode($errors));
                return redirect()->back()->withInput()->with('errors', $errors);
            }

            return redirect()->to('/staff/livestock-farm-blocks')->with('success', 'Livestock farm block created successfully!');
        } catch (\Exception $e) {
            log_message('error', '[Create Livestock Farm Block] Exception: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Failed to create livestock farm block: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified livestock farm block
     */
    public function show($id)
    {
        $district_id = session()->get('district_id');
        
        $farmBlock = $this->farmBlockModel
            ->select('livestock_farm_blocks.*,
                     farmer_information.given_name,
                     farmer_information.surname,
                     farmer_information.farmer_code,
                     adx_ward.name as ward_name,
                     adx_llg.name as llg_name,
                     adx_district.name as district_name,
                     adx_province.name as province_name')
            ->join('farmer_information', 'farmer_information.id = livestock_farm_blocks.farmer_id')
            ->join('adx_ward', 'adx_ward.id = livestock_farm_blocks.ward_id')
            ->join('adx_llg', 'adx_llg.id = livestock_farm_blocks.llg_id')
            ->join('adx_district', 'adx_district.id = livestock_farm_blocks.district_id')
            ->join('adx_province', 'adx_province.id = livestock_farm_blocks.province_id')
            ->where('livestock_farm_blocks.id', $id)
            ->where('livestock_farm_blocks.district_id', $district_id)
            ->where('livestock_farm_blocks.status !=', 'deleted')
            ->first();
        
        if (!$farmBlock) {
            return redirect()->to('/staff/livestock-farm-blocks')->with('error', 'Livestock farm block not found.');
        }

        $data = [
            'title' => 'View Livestock Farm Block',
            'page_header' => 'Livestock Farm Block Details',
            'farm_block' => $farmBlock
        ];

        return view('staff/staff_livestock_blocks/staff_livestock_blocks_show', $data);
    }

    /**
     * Show the form for editing the specified livestock farm block
     */
    public function edit($id)
    {
        $district_id = session()->get('district_id');
        
        $farmBlock = $this->farmBlockModel->where('id', $id)
                                         ->where('district_id', $district_id)
                                         ->where('status !=', 'deleted')
                                         ->first();
        
        if (!$farmBlock) {
            return redirect()->to('/staff/livestock-farm-blocks')->with('error', 'Livestock farm block not found.');
        }

        $data = [
            'title' => 'Edit Livestock Farm Block',
            'page_header' => 'Edit Livestock Farm Block',
            'farm_block' => $farmBlock,
            'farmers' => $this->farmerModel->where('district_id', $district_id)
                                          ->where('status', 'active')
                                          ->findAll(),
            'llgs' => $this->llgModel->where('district_id', $district_id)->findAll(),
            'wards' => $this->wardModel->where('district_id', $district_id)->findAll()
        ];

        return view('staff/staff_livestock_blocks/staff_livestock_blocks_edit', $data);
    }

    /**
     * Update the specified livestock farm block
     */
    public function update($id)
    {
        $district_id = session()->get('district_id');
        
        $farmBlock = $this->farmBlockModel->where('id', $id)
                                         ->where('district_id', $district_id)
                                         ->where('status !=', 'deleted')
                                         ->first();
        
        if (!$farmBlock) {
            return redirect()->to('/staff/livestock-farm-blocks')->with('error', 'Livestock farm block not found.');
        }

        $validation = \Config\Services::validation();
        
        $rules = [
            'farmer_id' => 'required|numeric',
            'llg_id' => 'required|numeric',
            'ward_id' => 'required|numeric',
            'village' => 'required|max_length[100]',
            'block_site' => 'required|max_length[200]',
            'lon' => 'permit_empty|max_length[50]',
            'lat' => 'permit_empty|max_length[50]',
            'remarks' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        // Handle block code regeneration if farmer changed
        $new_farmer_id = $this->request->getPost('farmer_id');
        $block_code = $farmBlock['block_code'];
        
        if ($new_farmer_id != $farmBlock['farmer_id']) {
            $farmer = $this->farmerModel->find($new_farmer_id);
            if (!$farmer) {
                return redirect()->back()->withInput()->with('error', 'Invalid farmer selected.');
            }

            // Check if block code needs updating (different farmer)
            $current_farmer_code = substr($farmBlock['block_code'], 2, strpos($farmBlock['block_code'], '-') - 2);
            if ($current_farmer_code !== $farmer['farmer_code']) {
                // Generate new block code for new farmer
                $latest_block = $this->farmBlockModel->where('farmer_id', $new_farmer_id)
                                                    ->orderBy('id', 'DESC')
                                                    ->first();

                if ($latest_block) {
                    $current_number = (int)substr($latest_block['block_code'], strpos($latest_block['block_code'], '-') + 1);
                    $block_code = 'LS' . $farmer['farmer_code'] . '-' . sprintf('%03d', $current_number + 1);
                } else {
                    $block_code = 'LS' . $farmer['farmer_code'] . '-001';
                }
            }
        }

        $data = [
            'farmer_id' => $new_farmer_id,
            'block_code' => $block_code,
            'llg_id' => $this->request->getPost('llg_id'),
            'ward_id' => $this->request->getPost('ward_id'),
            'village' => $this->request->getPost('village'),
            'block_site' => $this->request->getPost('block_site'),
            'lon' => $this->request->getPost('lon'),
            'lat' => $this->request->getPost('lat'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => session()->get('emp_id')
        ];

        try {
            $this->farmBlockModel->update($id, $data);
            return redirect()->to('/staff/livestock-farm-blocks')->with('success', 'Livestock farm block updated successfully!');
        } catch (\Exception $e) {
            log_message('error', '[Update Livestock Farm Block] ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Failed to update livestock farm block. Please try again.');
        }
    }

    /**
     * Remove the specified livestock farm block (soft delete)
     */
    public function destroy($id)
    {
        $district_id = session()->get('district_id');
        
        $farmBlock = $this->farmBlockModel->where('id', $id)
                                         ->where('district_id', $district_id)
                                         ->where('status !=', 'deleted')
                                         ->first();
        
        if (!$farmBlock) {
            return redirect()->to('/staff/livestock-farm-blocks')->with('error', 'Livestock farm block not found.');
        }

        try {
            // Soft delete by updating status
            $this->farmBlockModel->update($id, [
                'status' => 'deleted',
                'deleted_by' => session()->get('emp_id'),
                'deleted_at' => date('Y-m-d H:i:s')
            ]);
            
            return redirect()->to('/staff/livestock-farm-blocks')->with('success', 'Livestock farm block deleted successfully!');
        } catch (\Exception $e) {
            log_message('error', '[Delete Livestock Farm Block] ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to delete livestock farm block. Please try again.');
        }
    }


}
