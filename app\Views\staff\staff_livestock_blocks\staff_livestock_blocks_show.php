<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0">Livestock Farm Block Details</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>" class="text-primary">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/livestock-farm-blocks') ?>" class="text-primary">Livestock Farm Blocks</a></li>
                            <li class="breadcrumb-item active" aria-current="page">View Details</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff/livestock-farm-blocks') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                    <a href="<?= base_url('staff/livestock-farm-blocks/' . $farm_block['id'] . '/edit') ?>" class="btn btn-warning me-2">
                        <i class="fas fa-edit me-2"></i>Edit Block
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cow me-2"></i>Livestock Farm Block Information
                    </h5>
                    <p class="card-text mb-0 text-muted">Detailed information about this livestock farm block</p>
                </div>
                <div class="card-body">
                    <!-- Alert Messages -->
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i><?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>Basic Information
                            </h6>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Block Code:</label>
                                <p class="mb-0"><strong class="text-primary"><?= esc($farm_block['block_code']) ?></strong></p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Farmer:</label>
                                <p class="mb-0">
                                    <strong><?= esc($farm_block['given_name'] . ' ' . $farm_block['surname']) ?></strong>
                                    <br><small class="text-muted">Farmer Code: <?= esc($farm_block['farmer_code'] ?? 'N/A') ?></small>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Status:</label>
                                <p class="mb-0">
                                    <?php if ($farm_block['status'] == 'active'): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php elseif ($farm_block['status'] == 'inactive'): ?>
                                        <span class="badge bg-secondary">Inactive</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary"><?= ucfirst(esc($farm_block['status'])) ?></span>
                                    <?php endif; ?>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Village:</label>
                                <p class="mb-0"><?= esc($farm_block['village']) ?></p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Block Site:</label>
                                <p class="mb-0"><?= esc($farm_block['block_site']) ?></p>
                            </div>
                        </div>

                        <!-- Location Information -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-map-marker-alt me-2"></i>Location Information
                            </h6>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Province:</label>
                                <p class="mb-0"><?= esc($farm_block['province_name']) ?></p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">District:</label>
                                <p class="mb-0"><?= esc($farm_block['district_name']) ?></p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">LLG:</label>
                                <p class="mb-0"><?= esc($farm_block['llg_name']) ?></p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Ward:</label>
                                <p class="mb-0"><?= esc($farm_block['ward_name']) ?></p>
                            </div>

                            <?php if (!empty($farm_block['lon']) || !empty($farm_block['lat'])): ?>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Coordinates:</label>
                                <p class="mb-0">
                                    <?php if (!empty($farm_block['lat'])): ?>
                                        <strong>Latitude:</strong> <?= esc($farm_block['lat']) ?><br>
                                    <?php endif; ?>
                                    <?php if (!empty($farm_block['lon'])): ?>
                                        <strong>Longitude:</strong> <?= esc($farm_block['lon']) ?>
                                    <?php endif; ?>
                                </p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if (!empty($farm_block['remarks'])): ?>
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-comment me-2"></i>Remarks
                            </h6>
                            <div class="bg-light p-3 rounded">
                                <p class="mb-0"><?= nl2br(esc($farm_block['remarks'])) ?></p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Metadata -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-clock me-2"></i>Record Information
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong>Created:</strong> 
                                        <?= isset($farm_block['created_at']) ? date('M d, Y H:i', strtotime($farm_block['created_at'])) : 'N/A' ?>
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong>Last Updated:</strong> 
                                        <?= isset($farm_block['updated_at']) ? date('M d, Y H:i', strtotime($farm_block['updated_at'])) : 'N/A' ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="<?= base_url('staff/livestock-farm-blocks') ?>" class="btn btn-secondary">
                                    <i class="fas fa-list me-2"></i>Back to List
                                </a>
                                <a href="<?= base_url('staff/livestock-farm-blocks/' . $farm_block['id'] . '/edit') ?>" class="btn btn-warning">
                                    <i class="fas fa-edit me-2"></i>Edit Block
                                </a>
                                <form method="post" action="<?= base_url('staff/livestock-farm-blocks/' . $farm_block['id']) ?>" 
                                      style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this livestock farm block?')">
                                    <input type="hidden" name="_method" value="DELETE">
                                    <?= csrf_field() ?>
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-trash me-2"></i>Delete Block
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
